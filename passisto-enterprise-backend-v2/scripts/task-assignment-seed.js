const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * This seed script creates a workflow scenario with an unassigned task ready for UI assignment.
 * It creates:
 * 1. A demo company
 * 2. A workflow with a task node that requires user input
 * 3. A workflow run with an unassigned task node waiting for assignment from UI
 *
 * Note: This script works with existing users and roles in the system.
 */
async function main() {
  // Hard-coded user ID to prevent constraint violations
  const userId = 'ba4ec1da-50fe-4e3b-876f-fcb4ecf8a0b3';
  try {
    console.log('Starting task assignment seed...');
    console.log('Using user ID:', userId);

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error(`User with ID ${userId} not found. Please provide a valid user ID.`);
    }

    console.log(`Found user: ${user.firstName} ${user.lastName} (${user.email})`);

    // Clean up existing workflow data in database
    // console.log('Cleaning up existing workflow data...');
    // await prisma.nodeRun.deleteMany({});
    // await prisma.workflowRun.deleteMany({});
    // await prisma.workflow.deleteMany({});
    // await prisma.userGroup.deleteMany({});
    // await prisma.group.deleteMany({});
    // await prisma.company.deleteMany({});

    // Create company
    console.log('Creating company...');
    const company = await prisma.company.create({
      data: {
        name: 'Task Assignment Demo Company',
        description: 'Demo company for task assignment workflow testing'
      }
    });

    // Note: This script works with existing users and roles in the system

    // Create a workflow with a task node that requires user input
    console.log('Creating workflow with task node...');
    const workflow = await prisma.workflow.create({
      data: {
        name: 'Document Review Workflow',
        description: 'Review and approve documents',
        status: 'active',
        userId: userId, // Use provided user ID to prevent constraint violation
        nodes: [
          {
            id: 'start',
            type: 'start',
            position: { x: 250, y: 5 },
            data: { 
              label: 'Start',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'task-review',
            type: 'task',
            position: { x: 250, y: 100 },
            data: {
              label: 'Review Document',
              description: 'Please review the attached document and provide your feedback',
              // No assignee - task will be assigned from UI
              isActive: false,
              isCompleted: false,
              formFields: [
                {
                  id: 'feedback',
                  type: 'textarea',
                  label: 'Feedback',
                  placeholder: 'Enter your feedback here',
                  required: true
                },
                {
                  id: 'approved',
                  type: 'checkbox',
                  label: 'Approved',
                  required: true
                }
              ]
            }
          },
          {
            id: 'ask-ai',
            type: 'ask-ai',
            position: { x: 250, y: 200 },
            data: { 
              label: 'Analyze Feedback',
              model: 'gpt-4o',
              prompt: 'Analyze the feedback provided and suggest improvements',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'end',
            type: 'end',
            position: { x: 250, y: 300 },
            data: { 
              label: 'End',
              isActive: false,
              isCompleted: false
            }
          }
        ],
        edges: [
          {
            id: 'edge-start-task',
            source: 'start',
            target: 'task-review',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-task-ai',
            source: 'task-review',
            target: 'ask-ai',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-ai-end',
            source: 'ask-ai',
            target: 'end',
            style: { strokeWidth: 2 }
          }
        ],
        viewport: {
          x: 0,
          y: 0,
          zoom: 1
        },
      }
    });

    // Create a workflow run
    console.log('Creating workflow run...');
    const workflowRun = await prisma.workflowRun.create({
      data: {
        workflowId: workflow.id,
        status: 'RUNNING',
        startedAt: new Date(),
        createdAt: new Date()
      }
    });

    // Create node runs for each node in the workflow
    console.log('Creating node runs...');
    
    // Start node (completed)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'start',
        status: 'SUCCESS',
        startedAt: new Date(Date.now() - 60000), // 1 minute ago
        finishedAt: new Date(Date.now() - 55000), // 55 seconds ago
        output: { result: 'Workflow started successfully' }
      }
    });

    // Task node (waiting for assignment from UI)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'task-review',
        status: 'WAITING_FOR_USER',
        startedAt: new Date(Date.now() - 55000), // 55 seconds ago
        // No assigneeId or assignedAt - task will be assigned later from UI
        output: {
          taskDetails: {
            title: 'Review Document',
            description: 'Please review the attached document and provide your feedback',
            documentUrl: 'https://example.com/documents/sample.pdf',
            dueDate: new Date(Date.now() + 86400000) // Due in 24 hours
          }
        }
      }
    });

    // Update workflow run status to WAITING_FOR_USER
    await prisma.workflowRun.update({
      where: { id: workflowRun.id },
      data: { status: 'WAITING_FOR_USER' }
    });

    console.log('Workflow seed completed successfully!');
    console.log('\n=== CREATED RESOURCES ===');
    console.log('Company ID:', company.id);
    console.log('Workflow ID:', workflow.id);
    console.log('Workflow run ID:', workflowRun.id);
    console.log('\n=== WORKFLOW DETAILS ===');
    console.log('Workflow Name: Document Review Workflow');
    console.log('Workflow Creator:', `${user.firstName} ${user.lastName} (${user.email})`);
    console.log('Task Status: Waiting for assignment from UI');
    console.log('Task Title: Review Document');
    console.log('\n=== NEXT STEPS ===');
    console.log('1. Task is available and waiting for assignment from UI');
    console.log('2. Use the workflow run ID to assign and track task status');
    console.log('3. Assign the "Review Document" task to any user from the UI');
    console.log('4. The workflow creator can assign tasks through the UI');

  } catch (error) {
    console.error('Error seeding database:', error);
    if (error.message.includes('User with ID')) {
      console.error('\nTip: You can find existing user IDs by querying your database:');
      console.error('SELECT id, "firstName", "lastName", email FROM "User" LIMIT 5;');
    }
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
