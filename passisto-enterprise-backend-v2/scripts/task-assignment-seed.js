const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { clerkClient } = require('@clerk/express');
const { v4: uuidv4 } = require('uuid');
const { createOrGetClerkUser, updateClerkUserMetadata } = require('../src/utils/clerk-utils');
const { truncateClerkUsers } = require('../src/utils/clerk-admin');

const prisma = new PrismaClient();

/**
 * Helper function to create a user with Clerk integration using the invitation flow
 */
async function createUserWithClerkAndInvitation(userData, company, role) {
  console.log(`Creating user with invitation: ${userData.email}...`);

  try {
    // Step 1: Create invitation first
    const invitation = await prisma.invitation.create({
      data: {
        email: userData.email,
        companyId: company.id
      }
    });
    console.log(`Created invitation for ${userData.email}`);

    // Step 2: Create user in Clerk
    const clerkUser = await createOrGetClerkUser({
      email: userData.email,
      password: userData.password,
      firstName: userData.firstName,
      lastName: userData.lastName
    });

    if (!clerkUser) {
      throw new Error(`Failed to create Clerk user for ${userData.email}`);
    }

    // Step 3: Hash password for database
    const hashedPassword = await bcrypt.hash(userData.password, 10);

    // Step 4: Create user in database (this simulates the webhook flow)
    const user = await prisma.user.create({
      data: {
        clerkId: clerkUser.id,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        password: hashedPassword,
        companyId: company.id,
        hasCompletedOnboarding: true, // Mark as completed since this is a seed
        roles: {
          connect: { id: role.id }
        }
      }
    });

    // Step 5: Delete the invitation (simulates the webhook cleanup)
    await prisma.invitation.delete({
      where: { email: userData.email }
    });

    // Step 6: Update Clerk metadata
    await updateClerkUserMetadata(clerkUser.id, {
      companyId: company.id,
      userId: user.id,
      seedUser: true // Mark as seed user for cleanup
    });

    console.log(`Successfully created user: ${user.email} (${user.id})`);
    return user;
  } catch (error) {
    console.error(`Error creating user ${userData.email}:`, error);
    throw error;
  }
}

/**
 * This seed script creates a scenario where a task is assigned to a regular user.
 * It creates:
 * 1. A company and roles
 * 2. An admin user who creates the workflow
 * 3. A regular user who will be assigned the task
 * 4. A workflow with a task node that requires user input
 * 5. A workflow run with the task node assigned to the regular user
 * 6. Users are created using the invitation flow with proper Clerk integration
 */
async function main() {
  try {
    console.log('Starting task assignment seed...');

    // Clean up existing seeded users in Clerk
    console.log('Cleaning up existing Clerk users...');
    await truncateClerkUsers();

    // Clean up existing data in database
    console.log('Cleaning up existing database data...');
    await prisma.nodeRun.deleteMany({});
    await prisma.workflowRun.deleteMany({});
    await prisma.workflow.deleteMany({});
    await prisma.userGroup.deleteMany({});
    await prisma.user.deleteMany({});
    await prisma.invitation.deleteMany({});
    await prisma.group.deleteMany({});
    await prisma.company.deleteMany({});
    await prisma.role.deleteMany({});

    // Create company
    console.log('Creating company...');
    const company = await prisma.company.create({
      data: {
        name: 'Task Assignment Demo Company',
        description: 'Demo company for task assignment workflow testing'
      }
    });

    // Create roles
    console.log('Creating roles...');
    const adminRole = await prisma.role.create({
      data: { name: 'ADMIN' }
    });

    const userRole = await prisma.role.create({
      data: { name: 'USER' }
    });

    // Create users with Clerk integration using invitation flow
    const adminUser = await createUserWithClerkAndInvitation({
      email: '<EMAIL>',
      password: 'admin123456',
      firstName: 'Admin',
      lastName: 'Manager'
    }, company, adminRole);

    const regularUser = await createUserWithClerkAndInvitation({
      email: '<EMAIL>',
      password: 'john123456',
      firstName: 'John',
      lastName: 'Employee'
    }, company, userRole);

    await createUserWithClerkAndInvitation({
      email: '<EMAIL>',
      password: 'sarah123456',
      firstName: 'Sarah',
      lastName: 'Teammate'
    }, company, userRole);

    // Create a workflow with a task node that requires user input
    console.log('Creating workflow with task node...');
    const workflow = await prisma.workflow.create({
      data: {
        name: 'Document Review Workflow',
        description: 'Review and approve documents',
        status: 'active',
        userId: adminUser.id,
        nodes: [
          {
            id: 'start',
            type: 'start',
            position: { x: 250, y: 5 },
            data: { 
              label: 'Start',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'task-review',
            type: 'task',
            position: { x: 250, y: 100 },
            data: { 
              label: 'Review Document',
              description: 'Please review the attached document and provide your feedback',
              assignee: regularUser.id, // Pre-assign the task to John
              isActive: false,
              isCompleted: false,
              formFields: [
                {
                  id: 'feedback',
                  type: 'textarea',
                  label: 'Feedback',
                  placeholder: 'Enter your feedback here',
                  required: true
                },
                {
                  id: 'approved',
                  type: 'checkbox',
                  label: 'Approved',
                  required: true
                }
              ]
            }
          },
          {
            id: 'ask-ai',
            type: 'ask-ai',
            position: { x: 250, y: 200 },
            data: { 
              label: 'Analyze Feedback',
              model: 'gpt-4o',
              prompt: 'Analyze the feedback provided and suggest improvements',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'end',
            type: 'end',
            position: { x: 250, y: 300 },
            data: { 
              label: 'End',
              isActive: false,
              isCompleted: false
            }
          }
        ],
        edges: [
          {
            id: 'edge-start-task',
            source: 'start',
            target: 'task-review',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-task-ai',
            source: 'task-review',
            target: 'ask-ai',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-ai-end',
            source: 'ask-ai',
            target: 'end',
            style: { strokeWidth: 2 }
          }
        ],
        viewport: {
          x: 0,
          y: 0,
          zoom: 1
        },
      }
    });

    // Create a workflow run
    console.log('Creating workflow run...');
    const workflowRun = await prisma.workflowRun.create({
      data: {
        workflowId: workflow.id,
        status: 'RUNNING',
        startedAt: new Date(),
        createdAt: new Date()
      }
    });

    // Create node runs for each node in the workflow
    console.log('Creating node runs...');
    
    // Start node (completed)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'start',
        status: 'SUCCESS',
        startedAt: new Date(Date.now() - 60000), // 1 minute ago
        finishedAt: new Date(Date.now() - 55000), // 55 seconds ago
        output: { result: 'Workflow started successfully' }
      }
    });

    // Task node (waiting for user)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'task-review',
        status: 'WAITING_FOR_USER',
        startedAt: new Date(Date.now() - 55000), // 55 seconds ago
        assigneeId: regularUser.id,
        assignedAt: new Date(Date.now() - 55000), // 55 seconds ago
        output: { 
          taskDetails: {
            title: 'Review Document',
            description: 'Please review the attached document and provide your feedback',
            documentUrl: 'https://example.com/documents/sample.pdf',
            dueDate: new Date(Date.now() + 86400000) // Due in 24 hours
          }
        }
      }
    });

    // Update workflow run status to WAITING_FOR_USER
    await prisma.workflowRun.update({
      where: { id: workflowRun.id },
      data: { status: 'WAITING_FOR_USER' }
    });

    console.log('Task assignment seed completed successfully!');
    console.log('\n=== LOGIN CREDENTIALS ===');
    console.log('Admin: <EMAIL> / admin123456');
    console.log('User with assigned task: <EMAIL> / john123456');
    console.log('Another user: <EMAIL> / sarah123456');
    console.log('\n=== CREATED RESOURCES ===');
    console.log('Company ID:', company.id);
    console.log('Workflow ID:', workflow.id);
    console.log('Workflow run ID:', workflowRun.id);
    console.log('\n=== CLERK INTEGRATION ===');
    console.log('All users created using invitation flow with Clerk integration');
    console.log('Users marked as seedUser: true for easy cleanup');
    console.log('Invitations processed and cleaned up automatically');
    console.log('\n=== NEXT STEPS ===');
    console.log('1. Users can log in through Clerk authentication');
    console.log('2. John has a pending task assignment in the workflow');
    console.log('3. Use the workflow run ID to check task status');
    console.log('4. All users have completed onboarding automatically');

  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
