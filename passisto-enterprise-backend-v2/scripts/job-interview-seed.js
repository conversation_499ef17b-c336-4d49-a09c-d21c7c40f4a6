const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * This seed script creates a job interview workflow scenario.
 * It creates:
 * 1. A workflow that processes job interview recordings
 * 2. The workflow includes speech-to-text, AI analysis, HR review, and email notification
 *
 * Note: This script works with existing users in the system.
 */
async function main() {
  // Hard-coded user ID to prevent constraint violations
  const userId = 'ba4ec1da-50fe-4e3b-876f-fcb4ecf8a0b3';

  try {
    console.log('Starting job interview workflow seed...');
    console.log('Using user ID:', userId);

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error(`User with ID ${userId} not found. Please provide a valid user ID.`);
    }

    console.log(`Found user: ${user.firstName} ${user.lastName} (${user.email})`);

    // Note: This script works with existing users in the system

    // Create a workflow for job interview processing
    console.log('Creating job interview workflow...');
    const workflow = await prisma.workflow.create({
      data: {
        name: 'Job Interview Analysis Workflow',
        description: 'Process job interview recordings, generate AI analysis, and collaborate on candidate evaluation',
        status: 'active',
        userId: userId,
        nodes: [
          {
            id: 'start',
            type: 'start',
            position: { x: 600, y: 50 },
            data: {
              label: 'Start Interview Process',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'task-recruiter-upload',
            type: 'task',
            position: { x: 600, y: 250 },
            data: {
              label: 'Recruiter: Upload Interview Recording',
              description: 'Upload the audio recording of the candidate interview',
              assignee: recruiter.id,
              requiresUserInput: true,
              isActive: false,
              isCompleted: false,
              formFields: [
                {
                  id: 'candidate_name',
                  type: 'text',
                  label: 'Candidate Name',
                  placeholder: 'Enter the candidate\'s full name',
                  required: true
                },
                {
                  id: 'position',
                  type: 'text',
                  label: 'Position Applied For',
                  placeholder: 'Enter the job position',
                  required: true
                },
                {
                  id: 'interview_date',
                  type: 'date',
                  label: 'Interview Date',
                  required: true
                }
              ]
            }
          },
          {
            id: 'speech-to-text',
            type: 'speech-to-text',
            position: { x: 600, y: 450 },
            data: {
              label: 'Transcribe Interview Recording',
              description: 'Convert the interview audio to text for analysis',
              sourceType: 'audio',
              demoMode: true,
              service: 'revai',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'email-transcription-complete',
            type: 'email',
            position: { x: 600, y: 650 },
            data: {
              label: 'Notify HR of Transcription',
              to: '<EMAIL>',
              subject: 'Interview Transcription Complete',
              body: 'The interview recording has been successfully transcribed and is ready for AI analysis. The workflow will proceed automatically.',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'ask-ai-analysis',
            type: 'ask-ai',
            position: { x: 600, y: 850 },
            data: {
              label: 'Generate Interview Analysis',
              model: 'google/gemini-2.0-flash-thinking-exp:free',
              prompt: 'Analyze the following job interview transcript and provide a comprehensive report including:\n\n1. Candidate strengths and weaknesses\n2. Technical skills assessment\n3. Communication skills evaluation\n4. Cultural fit analysis\n5. Overall recommendation (Hire, Consider, or Reject)\n\nFormat the report in a professional structure with clear sections and bullet points where appropriate.',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'task-hr-review',
            type: 'task',
            position: { x: 600, y: 1050 },
            data: {
              label: 'HR Manager Review',
              description: 'Review the AI-generated interview analysis and provide your feedback',
              assignee: hrManager.id,
              requiresUserInput: true,
              isActive: false,
              isCompleted: false,
              formFields: [
                {
                  id: 'feedback',
                  type: 'textarea',
                  label: 'HR Feedback',
                  placeholder: 'Enter your feedback on the interview analysis',
                  required: true
                },
                {
                  id: 'rating',
                  type: 'select',
                  label: 'Candidate Rating',
                  options: [
                    { value: 'excellent', label: 'Excellent' },
                    { value: 'good', label: 'Good' },
                    { value: 'average', label: 'Average' },
                    { value: 'below_average', label: 'Below Average' },
                    { value: 'poor', label: 'Poor' }
                  ],
                  required: true
                },
                {
                  id: 'proceed_to_next_round',
                  type: 'checkbox',
                  label: 'Proceed to Next Round',
                  required: true
                }
              ]
            }
          },
          {
            id: 'decision-candidate-quality',
            type: 'decision',
            position: { x: 600, y: 1250 },
            data: {
              label: 'Evaluate Candidate Quality',
              condition: "inputs['task-hr-review'].rating === 'excellent' || inputs['task-hr-review'].rating === 'good'",
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'task-hiring-manager',
            type: 'task',
            position: { x: 100, y: 1450 },
            data: {
              label: 'Hiring Manager Review',
              description: 'Review the promising candidate\'s interview analysis and HR feedback',
              assignee: hrManager.id,
              requiresUserInput: true,
              isActive: false,
              isCompleted: false,
              formFields: [
                {
                  id: 'technical_assessment',
                  type: 'textarea',
                  label: 'Technical Assessment',
                  placeholder: 'Provide your assessment of the candidate\'s technical skills',
                  required: true
                },
                {
                  id: 'final_decision',
                  type: 'select',
                  label: 'Final Decision',
                  options: [
                    { value: 'hire', label: 'Hire' },
                    { value: 'reject', label: 'Reject' },
                    { value: 'additional_interview', label: 'Request Additional Interview' }
                  ],
                  required: true
                }
              ]
            }
          },
          {
            id: 'email-rejection',
            type: 'email',
            position: { x: 1100, y: 1450 },
            data: {
              label: 'Send Rejection Notification',
              to: '<EMAIL>',
              subject: 'Candidate Rejection Notification',
              body: 'This candidate did not meet our requirements based on the interview assessment. Please prepare a rejection letter with appropriate feedback.',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'decision-final-hire',
            type: 'decision',
            position: { x: 0, y: 1650 },
            data: {
              label: 'Is Candidate Hired?',
              condition: "inputs['task-hiring-manager'].final_decision === 'hire'",
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'decision-additional-interview',
            type: 'decision',
            position: { x: 200, y: 1650 },
            data: {
              label: 'Need Additional Interview?',
              condition: "inputs['task-hiring-manager'].final_decision === 'hire'",
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'email-offer',
            type: 'email',
            position: { x: -100, y: 1850 },
            data: {
              label: 'Send Offer Preparation Email',
              to: '<EMAIL>',
              subject: 'Prepare Offer for Candidate',
              body: 'The hiring manager has approved this candidate for hire. Please prepare an offer letter based on the interview feedback and assessment.',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'email-additional-interview',
            type: 'email',
            position: { x: 200, y: 1850 },
            data: {
              label: 'Request Additional Interview',
              to: '<EMAIL>',
              subject: 'Schedule Additional Interview',
              body: 'The hiring manager has requested an additional interview with this candidate. Please schedule another round and notify the team.',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'email-team-notification',
            type: 'email',
            position: { x: 600, y: 2050 },
            data: {
              label: 'Notify Team of Outcome',
              to: '<EMAIL>',
              subject: 'Job Interview Process Complete',
              body: 'The job interview process for this candidate has been completed. Please review the attached analysis and decision summary.',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'end',
            type: 'end',
            position: { x: 600, y: 2250 },
            data: {
              label: 'End Process',
              isActive: false,
              isCompleted: false
            }
          }
        ],
        edges: [
          {
            id: 'edge-start-recruiter',
            source: 'start',
            target: 'task-recruiter-upload',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-recruiter-speech',
            source: 'task-recruiter-upload',
            target: 'speech-to-text',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-speech-email',
            source: 'speech-to-text',
            target: 'email-transcription-complete',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-email-ai',
            source: 'email-transcription-complete',
            target: 'ask-ai-analysis',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-ai-hr',
            source: 'ask-ai-analysis',
            target: 'task-hr-review',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-hr-decision',
            source: 'task-hr-review',
            target: 'decision-candidate-quality',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-decision-yes',
            source: 'decision-candidate-quality',
            target: 'task-hiring-manager',
            style: { strokeWidth: 2 },
            sourceHandle: 'yes'
          },
          {
            id: 'edge-decision-no',
            source: 'decision-candidate-quality',
            target: 'email-rejection',
            style: { strokeWidth: 2 },
            sourceHandle: 'no'
          },
          {
            id: 'edge-hiring-decision-hire',
            source: 'task-hiring-manager',
            target: 'decision-final-hire',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-hiring-decision-additional',
            source: 'task-hiring-manager',
            target: 'decision-additional-interview',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-decision-hire-yes',
            source: 'decision-final-hire',
            target: 'email-offer',
            style: { strokeWidth: 2 },
            sourceHandle: 'yes'
          },
          {
            id: 'edge-decision-hire-no',
            source: 'decision-final-hire',
            target: 'email-rejection',
            style: { strokeWidth: 2 },
            sourceHandle: 'no'
          },
          {
            id: 'edge-decision-additional-yes',
            source: 'decision-additional-interview',
            target: 'email-additional-interview',
            style: { strokeWidth: 2 },
            sourceHandle: 'yes'
          },
          {
            id: 'edge-decision-additional-no',
            source: 'decision-additional-interview',
            target: 'email-rejection',
            style: { strokeWidth: 2 },
            sourceHandle: 'no'
          },
          {
            id: 'edge-offer-team',
            source: 'email-offer',
            target: 'email-team-notification',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-additional-team',
            source: 'email-additional-interview',
            target: 'email-team-notification',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-rejection-team',
            source: 'email-rejection',
            target: 'email-team-notification',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-team-end',
            source: 'email-team-notification',
            target: 'end',
            style: { strokeWidth: 2 }
          }
        ],
        viewport: {
          x: 300,
          y: 0,
          zoom: 0.35
        },
      }
    });

    // Create a workflow run (optional - uncomment if you want to create a running instance)
    /*
    console.log('Creating workflow run...');
    const workflowRun = await prisma.workflowRun.create({
      data: {
        workflowId: workflow.id,
        status: 'RUNNING',
        startedAt: new Date(),
        createdAt: new Date()
      }
    });

    // Create node runs for each node in the workflow
    console.log('Creating node runs...');

    // Start node (completed)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'start',
        status: 'SUCCESS',
        startedAt: new Date(Date.now() - 60000), // 1 minute ago
        finishedAt: new Date(Date.now() - 55000), // 55 seconds ago
        output: { result: 'Workflow started successfully' }
      }
    });

    // Speech-to-text node (waiting for file upload)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'speech-to-text',
        status: 'WAITING_FOR_USER',
        startedAt: new Date(Date.now() - 55000), // 55 seconds ago
        output: {
          status: 'Waiting for interview recording upload'
        }
      }
    });

    // Update workflow run status to WAITING_FOR_USER
    await prisma.workflowRun.update({
      where: { id: workflowRun.id },
      data: { status: 'WAITING_FOR_USER' }
    });
    */

    console.log('Job interview workflow seed completed successfully!');
    console.log('\n=== CREATED RESOURCES ===');
    console.log('Workflow ID:', workflow.id);
    console.log('\n=== WORKFLOW DETAILS ===');
    console.log('Workflow Name: Job Interview Analysis Workflow');
    console.log('Workflow Creator:', `${user.firstName} ${user.lastName} (${user.email})`);
    console.log('\n=== NEXT STEPS ===');
    console.log('1. Use existing users in your system');
    console.log('2. Upload interview recordings to start the workflow');
    console.log('3. The workflow will process recordings through AI analysis');

  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
